{"productName": "MCP <PERSON>ced", "version": "2.4.3", "identifier": "com.minidoracat.mcp-feedback-enhanced", "build": {"frontendDist": "../src/mcp_feedback_enhanced/web/static", "devUrl": "http://127.0.0.1:8765", "beforeDevCommand": "", "beforeBuildCommand": ""}, "app": {"withGlobalTauri": true, "windows": [{"title": "MCP <PERSON>ced", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "alwaysOnTop": false, "skipTaskbar": false, "center": true, "url": "index.html"}], "security": {"csp": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' ws: wss: http: https:; font-src 'self' data:;"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": [], "externalBin": [], "copyright": "Copyright © 2024 Minidoracat", "category": "DeveloperTool", "shortDescription": "Enhanced MCP server for interactive user feedback", "longDescription": "An enhanced MCP server that provides interactive user feedback functionality for AI-assisted development, featuring Web UI with intelligent environment detection.", "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": ""}, "linux": {"deb": {"depends": []}}}, "plugins": {"shell": {"open": true}}}