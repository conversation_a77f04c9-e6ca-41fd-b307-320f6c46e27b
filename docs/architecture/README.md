# MCP Feedback Enhanced 架構文檔

## 📋 文檔索引

本目錄包含 MCP Feedback Enhanced 專案的完整架構文檔，提供深入的技術分析和設計說明。

### 📚 文檔結構

| 文檔 | 描述 | 適用對象 |
|------|------|----------|
| [系統架構總覽](./system-overview.md) | 整體架構設計、核心概念和技術亮點 | 架構師、技術負責人 |
| [組件詳細說明](./component-details.md) | 各層級組件的詳細功能和實現 | 開發人員、維護人員 |
| [交互流程文檔](./interaction-flows.md) | AI 助手與 MCP 服務的完整交互流程 | 集成開發人員 |
| [API 參考文檔](./api-reference.md) | MCP 工具接口和 WebSocket API 規範 | API 使用者、前端開發 |
| [部署指南](./deployment-guide.md) | 環境配置、部署選項和故障排除 | 運維人員、系統管理員 |

### 🏗️ 架構概覽

MCP Feedback Enhanced 採用**單一活躍會話 + 持久化 Web UI**的創新架構，實現了 AI 助手與用戶之間的無縫交互體驗。

#### 核心特性
- **智能環境檢測**: 自動識別 Local/SSH Remote/WSL 環境
- **單一活躍會話**: 替代傳統多會話管理，提升性能和用戶體驗
- **持久化 Web UI**: 支援多次循環調用，無需重複開啟瀏覽器
- **實時雙向通信**: WebSocket 實現前後端狀態同步
- **智能資源管理**: 自動清理和會話生命週期管理
- **提示詞管理系統**: 常用提示詞的 CRUD 操作和快速選擇
- **自動提交功能**: 倒數計時器和自動回饋提交機制
- **會話管理功能**: 會話歷史追蹤和統計分析（v2.4.3 重構增強）
- **音效通知系統**: 智能音效提醒和自訂音效管理（v2.4.3 新增）
- **智能記憶功能**: 輸入框高度記憶和一鍵複製（v2.4.3 新增）
- **多語言支援**: 繁體中文、簡體中文、英文動態切換

#### 技術棧
- **後端**: Python 3.11+, FastAPI, FastMCP
- **前端**: HTML5, JavaScript ES6+, WebSocket, Web Audio API（v2.4.3）
- **通信**: WebSocket, HTTP REST API
- **存儲**: localStorage（會話歷史、音效文件、設定記憶）
- **部署**: uvicorn, 跨平台支援

### 🎯 快速導航

- **新手入門**: 從 [系統架構總覽](./system-overview.md) 開始
- **深入理解**: 閱讀 [組件詳細說明](./component-details.md)
- **集成開發**: 參考 [交互流程文檔](./interaction-flows.md) 和 [API 參考文檔](./api-reference.md)
- **部署運維**: 查看 [部署指南](./deployment-guide.md)

### 📊 架構圖表

所有文檔都包含豐富的 Mermaid 圖表，包括：
- 系統整體架構圖
- 組件關係圖
- 交互流程圖
- 會話生命週期圖
- 部署拓撲圖
- **音效通知系統架構圖**（v2.4.3 新增）
- **會話管理重構流程圖**（v2.4.3 新增）
- **智能記憶功能架構圖**（v2.4.3 新增）

### 🆕 v2.4.3 版本亮點

#### 🔊 音效通知系統
- **內建音效**: 經典提示音、通知鈴聲、輕柔鐘聲
- **自訂音效**: 支援 MP3、WAV、OGG 格式上傳
- **智能播放**: 會話更新時自動播放通知音效
- **音量控制**: 0-100% 可調節音量
- **瀏覽器相容**: 處理自動播放政策限制

#### 📋 會話管理重構
- **頁籤化設計**: 從側邊欄遷移到獨立頁籤，解決瀏覽器相容性問題
- **本地歷史存儲**: 支援 72 小時可配置保存期限
- **隱私控制**: 三級用戶訊息記錄設定（完整/基本/停用）
- **數據管理**: 匯出和清理功能
- **詳情查看**: 專門的會話詳情彈窗

#### 🧠 智能記憶功能
- **輸入框高度記憶**: 自動保存和恢復輸入框高度
- **一鍵複製**: 專案路徑和會話ID點擊複製
- **設定持久化**: 用戶偏好自動保存
- **國際化支援**: 複製提示支援多語言

---

**版本**: 2.4.3
**最後更新**: 2025年6月14日
**維護者**: Minidoracat
**架構類型**: Web-Only 四層架構
**v2.4.3 新功能**: 音效通知系統、會話管理重構、智能記憶功能、一鍵複製
**歷史功能**: 提示詞管理、自動提交、會話管理、語系切換優化
**文檔狀態**: ✅ 已完成 v2.4.3 全面更新，包含所有新功能的詳細說明和架構分析
